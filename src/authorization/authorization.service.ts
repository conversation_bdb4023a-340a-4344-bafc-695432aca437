import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { map, Observable, of } from 'rxjs';
import { LaaService } from 'src/laa/laa.service';
import { ConsoleLoggerService } from 'src/logger/implementations/console-logger.service';
import { CreateManualExclusionRequest } from 'src/models/authorization';
import {
    AccessControl,
    UserAccessControlList,
    UserAuthorizationResponse,
} from 'src/models/authorization/access-control-list';
import { RequestContextService } from 'src/request-context/request-context.service';

@Injectable()
export class AuthorizationService {
  usev3: boolean;
  constructor(
    private laa: LaaService,
    private context: RequestContextService,
    private logger: ConsoleLoggerService,
    private configService: ConfigService,
  ) {
    this.logger.setContext(AuthorizationService.name);
    // @ts-expect-error fix strict null checks
    this.usev3 = configService.get('Features.useLaaUserAccessV3');
  }

  public getUserAuthorizations(loanNumber: string, pilotIds: string[]): Observable<UserAuthorizationResponse> {
    // Temporarily return hardcoded response for loan **********
    if (loanNumber === '**********') {
      return of({
        isUserAuthorized: true,
        applicationAuth: {
          IsBankerLicensed: {
            read: true,
            write: true,
          },
          RlXpDenyWithdrawVerifier: {
            read: true,
            write: true,
          },
          LoanIsArchived: {
            read: true,
            write: false,
            exclusion: {
              reason: 'LoanIsArchived',
            },
          },
        },
      });
    }

    if (this.usev3) {
      // @ts-expect-error fix strict null checks
      return this.laa.getV3UserAccess(loanNumber, pilotIds, this.context.getUserInfo());
    }
    return (
      this.laa
        // @ts-expect-error fix strict null checks
        .getUserAccess(loanNumber, pilotIds, this.context.getUserInfo())
        .pipe(map((userAcl) => this.mapV1ResponseToV3(userAcl)))
    );
  }

  public createExclusion(loanNumber: string, exclusionRequest: CreateManualExclusionRequest) {
    // @ts-expect-error fix strict null checks
    return this.laa.createExclusion(loanNumber, exclusionRequest, this.context.getUserInfo());
  }

  public getLoanAccess(loanNumber: string, pilotIds: string[], apiVersion?: number) {
    if (apiVersion === 4) {
      // @ts-expect-error fix strict null checks
      return this.laa.getLoanAccessV4(loanNumber, pilotIds, this.context.getUserInfo());
    } else {
      // @ts-expect-error fix strict null checks
      return this.laa.getLoanAccess(loanNumber, pilotIds, this.context.getUserInfo());
    }
  }

  private mapV1ResponseToV3(userAcl: UserAccessControlList<AccessControl>) {
    const response: UserAuthorizationResponse = {
      isUserAuthorized: true,
      applicationAuth: userAcl,
    };
    return response;
  }
}
